<?php

namespace App\Imports;

use App\Models\Cargo;
use App\Models\Driver;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class DriverImport implements ToCollection, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    protected $additionalData = [];
    protected $customImportData = [];
    protected $importStats = [
        'total' => 0,
        'imported' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => 0,
    ];
    protected $validationErrors = [];
    protected $cargoCache = [];

    public function collection(Collection $rows)
    {
        $this->importStats['total'] = $rows->count();

        // Send initial notification for large imports
        if ($rows->count() > 50) {
            $this->sendProgressNotification('start', $rows->count());
        }

        $successCount = 0;
        $updatedCount = 0;
        $errorCount = 0;
        $duplicatesInExcel = 0;
        $duplicatesNotImported = 0;
        $skippedCount = 0;
        $errors = [];
        $processedDnis = [];
        $duplicateDnis = [];

        // Pre-load existing drivers to optimize database queries
        $existingDrivers = Driver::pluck('id', 'dni')->toArray();

        DB::beginTransaction();

        try {
            foreach ($rows as $rowIndex => $row) {
                $rowNumber = $rowIndex + 2; // +2 because of 0-index and header row

                try {
                    // Use helper methods for flexible column mapping
                    $dni = $this->getDniFromRow($row);
                    $name = $this->getName($row);

                    // Skip empty rows with better validation
                    if (empty($dni) || empty($name)) {
                        $skippedCount++;
                        Log::info('Skipped empty row', ['row' => $rowNumber, 'data' => $row]);
                        continue;
                    }

                    // Validate row data
                    $validatedData = $this->validateRowData($row, $rowNumber);
                    if (!$validatedData) {
                        $errorCount++;
                        continue;
                    }

                    // Check for duplicates in Excel
                    if (in_array($dni, $processedDnis)) {
                        $duplicatesInExcel++;
                        $duplicateDnis[] = $dni;
                        $duplicatesNotImported++;

                        Log::warning('Duplicate DNI found in Excel', [
                            'dni' => $dni,
                            'row' => $rowNumber,
                            'data' => $row,
                        ]);
                        continue;
                    }

                    $processedDnis[] = $dni;

                    // Get or create cargo with caching
                    $cargo = $this->getOrCreateCargoWithCache($row);

                    // Prepare driver data
                    $driverData = [
                        'name' => $validatedData['name'],
                        'last_paternal_name' => $validatedData['last_paternal_name'],
                        'last_maternal_name' => $validatedData['last_maternal_name'],
                        'cargo_id' => $cargo?->id,
                        'status' => true, // Default to active
                    ];

                    // Check if driver exists
                    $isUpdate = isset($existingDrivers[$dni]);

                    // Create or update driver
                    Driver::updateOrCreate(
                        ['dni' => $dni],
                        $driverData
                    );

                    if ($isUpdate) {
                        $updatedCount++;
                        Log::info('Driver updated', ['dni' => $dni, 'row' => $rowNumber]);
                    } else {
                        $successCount++;
                        Log::info('Driver created', ['dni' => $dni, 'row' => $rowNumber]);
                    }
                } catch (ValidationException $e) {
                    $errorCount++;
                    $errorMessage = 'Fila ' . $rowNumber . " con DNI {$dni}: " . implode(', ', $e->errors()['general'] ?? $e->errors());
                    $errors[] = $errorMessage;
                    $this->validationErrors[] = $errorMessage;

                    Log::error('Validation error importing driver', [
                        'row' => $rowNumber,
                        'data' => $row,
                        'errors' => $e->errors(),
                    ]);
                } catch (\Exception $e) {
                    $errorCount++;
                    $errorMessage = 'Fila ' . $rowNumber . " con DNI {$dni}: " . $e->getMessage();
                    $errors[] = $errorMessage;

                    Log::error('Error importing driver', [
                        'row' => $rowNumber,
                        'data' => $row,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                }
            }

            // Update import stats
            $this->importStats['imported'] = $successCount;
            $this->importStats['updated'] = $updatedCount;
            $this->importStats['skipped'] = $skippedCount;
            $this->importStats['errors'] = $errorCount;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Critical error during driver import', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }

        // Send completion notification for large imports
        if ($this->importStats['total'] > 50) {
            $this->sendProgressNotification('validation');
        }

        // Send detailed notification with results
        $this->sendImportNotification($successCount, $updatedCount, $errorCount, $duplicatesInExcel, $duplicatesNotImported, $duplicateDnis, $skippedCount);
    }

    /**
     * Validate row data with comprehensive validation rules
     */
    private function validateRowData($row, int $rowNumber): ?array
    {
        try {
            $dni = $this->getDniFromRow($row);
            $name = $this->getName($row);
            $lastPaternalName = $this->getLastPaternalName($row);
            $lastMaternalName = $this->getLastMaternalName($row);

            // Validation rules
            $rules = [
                'dni' => ['required', 'string', 'min:8', 'max:12', 'regex:/^[0-9]+$/'],
                'name' => ['required', 'string', 'min:2', 'max:255'],
                'last_paternal_name' => ['nullable', 'string', 'max:255'],
                'last_maternal_name' => ['nullable', 'string', 'max:255'],
            ];

            $data = [
                'dni' => $dni,
                'name' => $name,
                'last_paternal_name' => $lastPaternalName,
                'last_maternal_name' => $lastMaternalName,
            ];

            $validator = Validator::make($data, $rules, [
                'dni.required' => 'El DNI es obligatorio',
                'dni.regex' => 'El DNI debe contener solo números',
                'dni.min' => 'El DNI debe tener al menos 8 dígitos',
                'dni.max' => 'El DNI no puede tener más de 12 dígitos',
                'name.required' => 'El nombre es obligatorio',
                'name.min' => 'El nombre debe tener al menos 2 caracteres',
                'name.max' => 'El nombre no puede tener más de 255 caracteres',
            ]);

            if ($validator->fails()) {
                $errorMessage = 'Fila ' . $rowNumber . ': ' . implode(', ', $validator->errors()->all());
                $this->validationErrors[] = $errorMessage;

                Log::warning('Row validation failed', [
                    'row' => $rowNumber,
                    'data' => $data,
                    'errors' => $validator->errors()->all(),
                ]);

                return null;
            }

            return $data;
        } catch (\Exception $e) {
            Log::error('Error validating row data', [
                'row' => $rowNumber,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get or create cargo with caching for better performance
     */
    private function getOrCreateCargoWithCache($row): ?Cargo
    {
        $cargoFields = ['CARGO', 'cargo', 'position', 'puesto', 'job_title'];
        $cargoName = null;

        foreach ($cargoFields as $field) {
            if (!empty($row[$field])) {
                $cargoName = trim((string) $row[$field]);
                break;
            }
        }

        if (empty($cargoName)) {
            return null;
        }

        // Use cache to avoid repeated database queries
        if (!isset($this->cargoCache[$cargoName])) {
            $this->cargoCache[$cargoName] = Cargo::firstOrCreate(
                ['name' => $cargoName],
                ['name' => $cargoName, 'status' => true]
            );
        }

        return $this->cargoCache[$cargoName];
    }

    /**
     * Enhanced notification system with comprehensive feedback
     */
    private function sendImportNotification(int $successCount, int $updatedCount, int $errorCount, int $duplicatesInExcel, int $duplicatesNotImported, array $duplicateDnis, int $skippedCount)
    {
        $totalProcessed = $successCount + $updatedCount + $errorCount + $duplicatesNotImported + $skippedCount;

        // Send main notification based on overall result
        $this->sendMainNotification($successCount, $updatedCount, $errorCount, $duplicatesInExcel, $skippedCount, $totalProcessed);

        // Send detailed notifications for specific issues
        if ($duplicatesInExcel > 0) {
            $this->sendDuplicatesNotification($duplicatesInExcel, $duplicatesNotImported, $duplicateDnis);
        }

        if ($errorCount > 0 && !empty($this->validationErrors)) {
            $this->sendErrorDetailsNotification($errorCount);
        }

        // Send summary notification for large imports
        if ($totalProcessed > 100) {
            $this->sendSummaryNotification($totalProcessed, $successCount, $updatedCount, $errorCount, $skippedCount);
        }

        // Send recommendations if there were issues
        if ($errorCount > 0 || $duplicatesInExcel > 0) {
            $this->sendRecommendationsNotification($errorCount, $duplicatesInExcel);
        }
    }

    /**
     * Send main import result notification
     */
    private function sendMainNotification(int $successCount, int $updatedCount, int $errorCount, int $duplicatesInExcel, int $skippedCount, int $totalProcessed)
    {
        $bodyParts = [];
        $hasSuccess = ($successCount > 0 || $updatedCount > 0);
        $hasIssues = ($errorCount > 0 || $duplicatesInExcel > 0);

        // Build success message
        if ($successCount > 0 && $updatedCount > 0) {
            $bodyParts[] = "✅ {$successCount} conductores nuevos creados";
            $bodyParts[] = "� {$updatedCount} conductores existentes actualizados";
        } elseif ($successCount > 0) {
            $bodyParts[] = "✅ {$successCount} conductores nuevos importados exitosamente";
        } elseif ($updatedCount > 0) {
            $bodyParts[] = "🔄 {$updatedCount} conductores actualizados correctamente";
        }

        // Add processing info
        if ($totalProcessed > 0) {
            $bodyParts[] = "📊 Total de registros procesados: {$totalProcessed}";
        }

        // Add issues summary
        if ($skippedCount > 0) {
            $bodyParts[] = "⏭️ {$skippedCount} filas omitidas (datos incompletos)";
        }

        if ($duplicatesInExcel > 0) {
            $bodyParts[] = "⚠️ {$duplicatesInExcel} duplicados detectados y omitidos";
        }

        if ($errorCount > 0) {
            $bodyParts[] = "❌ {$errorCount} registros con errores";
        }

        $body = implode("\n", $bodyParts);

        // Determine notification type and send
        if ($hasSuccess && !$hasIssues && $skippedCount === 0) {
            // Perfect import
            Notification::make()
                ->title('🎉 Importación Completada Exitosamente')
                ->body($body)
                ->success()
                ->duration(6000)
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_drivers')
                        ->label('Ver Conductores')
                        ->url(route('filament.admin.resources.drivers.index'))
                        ->button(),
                ])
                ->send();
        } elseif ($hasSuccess && ($hasIssues || $skippedCount > 0)) {
            // Partial success
            Notification::make()
                ->title('⚠️ Importación Completada con Observaciones')
                ->body($body)
                ->warning()
                ->duration(10000)
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_drivers')
                        ->label('Ver Conductores')
                        ->url(route('filament.admin.resources.drivers.index'))
                        ->button(),
                    \Filament\Notifications\Actions\Action::make('view_logs')
                        ->label('Ver Detalles')
                        ->action(function () {
                            $this->sendErrorDetailsNotification(count($this->validationErrors));
                        })
                        ->color('gray'),
                ])
                ->send();
        } else {
            // Failed import
            Notification::make()
                ->title('❌ Error en la Importación')
                ->body($body ?: 'No se pudieron procesar los registros del archivo.')
                ->danger()
                ->duration(15000)
                ->actions([
                    \Filament\Notifications\Actions\Action::make('retry')
                        ->label('Reintentar')
                        ->color('danger')
                        ->action(function () {
                            // Clear previous errors for retry
                            $this->clearValidationErrors();
                        }),
                ])
                ->send();
        }
    }

    /**
     * Send detailed notification about duplicates
     */
    private function sendDuplicatesNotification(int $duplicatesInExcel, int $duplicatesNotImported, array $duplicateDnis)
    {
        $uniqueDnis = array_unique($duplicateDnis);
        $bodyParts = [];

        $bodyParts[] = "Se detectaron {$duplicatesInExcel} registros duplicados en el archivo Excel.";
        $bodyParts[] = "Estos {$duplicatesNotImported} registros duplicados fueron omitidos automáticamente.";

        if (count($uniqueDnis) > 0) {
            $bodyParts[] = "\n📋 DNIs con duplicados detectados:";

            // Show up to 5 examples
            $examples = array_slice($uniqueDnis, 0, 5);
            foreach ($examples as $dni) {
                $occurrences = array_count_values($duplicateDnis)[$dni] ?? 1;
                $bodyParts[] = "• DNI {$dni} (aparece {$occurrences} veces)";
            }

            if (count($uniqueDnis) > 5) {
                $remaining = count($uniqueDnis) - 5;
                $bodyParts[] = "... y {$remaining} DNIs más con duplicados";
            }
        }

        $bodyParts[] = "\n💡 Recomendación: Revisa y limpia tu archivo Excel antes de importar para evitar duplicados.";

        Notification::make()
            ->title('� Reporte Detallado de Duplicados')
            ->body(implode("\n", $bodyParts))
            ->info()
            ->duration(15000)
            ->actions([
                \Filament\Notifications\Actions\Action::make('download_template')
                    ->label('Descargar Plantilla')
                    ->color('info')
                    ->action(function () {
                        // Action to download clean template
                    }),
            ])
            ->send();
    }

    /**
     * Send detailed error information
     */
    private function sendErrorDetailsNotification(int $errorCount)
    {
        $bodyParts = [];
        $bodyParts[] = "Se encontraron {$errorCount} errores durante la importación:";

        // Show first few validation errors as examples
        $errorExamples = array_slice($this->validationErrors, 0, 3);
        foreach ($errorExamples as $error) {
            $bodyParts[] = "• {$error}";
        }

        if (count($this->validationErrors) > 3) {
            $remaining = count($this->validationErrors) - 3;
            $bodyParts[] = "... y {$remaining} errores más";
        }

        $bodyParts[] = "\n💡 Consejos para corregir errores:";
        $bodyParts[] = "• Verifica que todos los DNIs tengan entre 8-12 dígitos";
        $bodyParts[] = "• Asegúrate de que los nombres no estén vacíos";
        $bodyParts[] = "• Revisa que no haya caracteres especiales en los DNIs";

        Notification::make()
            ->title('🔧 Detalles de Errores de Validación')
            ->body(implode("\n", $bodyParts))
            ->warning()
            ->duration(20000)
            ->actions([
                \Filament\Notifications\Actions\Action::make('export_errors')
                    ->label('Exportar Errores')
                    ->color('warning')
                    ->action(function () {
                        // Action to export error details
                        Log::info('Validation errors export requested', [
                            'errors' => $this->validationErrors,
                            'timestamp' => now(),
                        ]);
                    }),
            ])
            ->send();
    }

    /**
     * Send summary notification for large imports
     */
    private function sendSummaryNotification(int $totalProcessed, int $successCount, int $updatedCount, int $errorCount, int $skippedCount)
    {
        $successRate = $totalProcessed > 0 ? round((($successCount + $updatedCount) / $totalProcessed) * 100, 1) : 0;

        $bodyParts = [];
        $bodyParts[] = "📈 Resumen de Importación Masiva:";
        $bodyParts[] = "• Total procesados: {$totalProcessed} registros";
        $bodyParts[] = "• Tasa de éxito: {$successRate}%";
        $bodyParts[] = "• Nuevos: {$successCount} | Actualizados: {$updatedCount}";
        $bodyParts[] = "• Errores: {$errorCount} | Omitidos: {$skippedCount}";

        $bodyParts[] = "\n⏱️ Importación completada: " . now()->format('d/m/Y H:i:s');

        Notification::make()
            ->title('📊 Resumen de Importación')
            ->body(implode("\n", $bodyParts))
            ->info()
            ->duration(12000)
            ->actions([
                \Filament\Notifications\Actions\Action::make('view_report')
                    ->label('Ver Reporte Completo')
                    ->color('info')
                    ->action(function () {
                        Log::info('Import summary report', [
                            'stats' => $this->importStats,
                            'validation_errors' => $this->validationErrors,
                            'timestamp' => now(),
                        ]);
                    }),
            ])
            ->send();
    }

    /**
     * Send progress notifications for large imports
     */
    private function sendProgressNotification(string $stage, int $totalRows = 0, int $processed = 0)
    {
        switch ($stage) {
            case 'start':
                Notification::make()
                    ->title('🚀 Iniciando Importación')
                    ->body("Procesando {$totalRows} registros de conductores...\nEsto puede tomar unos momentos.")
                    ->info()
                    ->duration(3000)
                    ->send();
                break;

            case 'progress':
                $percentage = $totalRows > 0 ? round(($processed / $totalRows) * 100) : 0;
                Notification::make()
                    ->title('⏳ Importación en Progreso')
                    ->body("Progreso: {$processed}/{$totalRows} registros ({$percentage}%)")
                    ->info()
                    ->duration(2000)
                    ->send();
                break;

            case 'validation':
                Notification::make()
                    ->title('🔍 Validando Datos')
                    ->body('Verificando la integridad de los datos antes de guardar...')
                    ->info()
                    ->duration(2000)
                    ->send();
                break;
        }
    }

    /**
     * Send notification with actionable recommendations
     */
    // private function sendRecommendationsNotification(int $errorCount, int $duplicatesCount)
    // {
    //     if ($errorCount === 0 && $duplicatesCount === 0) {
    //         return; // No recommendations needed
    //     }

    //     $bodyParts = [];
    //     $bodyParts[] = "📋 Recomendaciones para mejorar futuras importaciones:";

    //     if ($errorCount > 0) {
    //         $bodyParts[] = "\n🔧 Para reducir errores:";
    //         $bodyParts[] = "• Usa la plantilla oficial de importación";
    //         $bodyParts[] = "• Verifica que los DNIs sean solo números";
    //         $bodyParts[] = "• Asegúrate de que todos los campos obligatorios estén completos";
    //         $bodyParts[] = "• Revisa que los nombres no contengan caracteres especiales";
    //     }

    //     if ($duplicatesCount > 0) {
    //         $bodyParts[] = "\n📊 Para evitar duplicados:";
    //         $bodyParts[] = "• Usa la función 'Quitar duplicados' de Excel";
    //         $bodyParts[] = "• Ordena por DNI para identificar duplicados visualmente";
    //         $bodyParts[] = "• Considera usar filtros avanzados en Excel";
    //     }

    //     $bodyParts[] = "\n💡 ¿Necesitas ayuda? Consulta la documentación o contacta al administrador.";

    //     Notification::make()
    //         ->title('💡 Consejos para Optimizar Importaciones')
    //         ->body(implode("\n", $bodyParts))
    //         ->info()
    //         ->duration(25000)
    //         ->actions([
    //             \Filament\Notifications\Actions\Action::make('download_guide')
    //                 ->label('Descargar Guía')
    //                 ->color('info')
    //                 ->action(function () {
    //                     Log::info('Import guide download requested');
    //                 }),
    //             \Filament\Notifications\Actions\Action::make('contact_support')
    //                 ->label('Contactar Soporte')
    //                 ->color('gray')
    //                 ->action(function () {
    //                     Log::info('Support contact requested for import help');
    //                 }),
    //         ])
    //         ->send();
    // }

    public function headingRow(): int
    {
        return 1; // First row contains headers
    }

    /**
     * Configure batch size for better performance
     */
    public function batchSize(): int
    {
        return 500; // Process 500 rows at a time
    }

    /**
     * Configure chunk size for memory efficiency
     */
    public function chunkSize(): int
    {
        return 1000; // Read 1000 rows at a time
    }

    /**
     * Obtener DNI de la fila con múltiples variaciones y validación mejorada
     */
    private function getDniFromRow($row): string
    {
        $dniFields = ['dni', 'documento', 'cedula', 'ci', 'numero_documento', 'document_number'];

        foreach ($dniFields as $field) {
            if (!empty($row[$field])) {
                $dni = trim((string) $row[$field]);
                // Remove any non-numeric characters for consistency
                $dni = preg_replace('/[^0-9]/', '', $dni);

                if ($this->isValidDni($dni)) {
                    return $dni;
                }
            }
        }

        return '';
    }

    /**
     * Obtener apellido paterno de la fila con sanitización
     */
    private function getLastPaternalName($row): ?string
    {
        $fields = [
            'apellido_paterno',
            'apellido paterno',
            'last_paternal_name',
            'primer_apellido',
            'primer apellido',
            'paternal_surname',
        ];

        foreach ($fields as $field) {
            if (!empty($row[$field])) {
                return $this->sanitizeText((string) $row[$field]);
            }
        }

        return null;
    }

    /**
     * Obtener apellido materno de la fila con sanitización
     */
    private function getLastMaternalName($row): ?string
    {
        $fields = [
            'apellido_materno',
            'apellido materno',
            'last_maternal_name',
            'segundo_apellido',
            'segundo apellido',
            'maternal_surname',
        ];

        foreach ($fields as $field) {
            if (!empty($row[$field])) {
                return $this->sanitizeText((string) $row[$field]);
            }
        }

        return null;
    }

    /**
     * Obtener nombre de la fila con sanitización
     */
    private function getName($row): string
    {
        $fields = [
            'name',
            'nombres',
            'nombre',
            'first_name',
            'nombre_completo',
            'full_name',
            'given_name',
        ];

        foreach ($fields as $field) {
            if (!empty($row[$field])) {
                $name = $this->sanitizeText((string) $row[$field]);
                return $name ?? '';
            }
        }

        return '';
    }

    /**
     * Get validation errors that occurred during import
     */
    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }

    /**
     * Clear validation errors
     */
    public function clearValidationErrors(): void
    {
        $this->validationErrors = [];
    }

    /**
     * Check if import has validation errors
     */
    public function hasValidationErrors(): bool
    {
        return !empty($this->validationErrors);
    }

    /**
     * Sanitize and normalize text input
     */
    private function sanitizeText(?string $text): ?string
    {
        if (empty($text)) {
            return null;
        }

        // Remove extra whitespace and normalize
        $text = trim($text);
        $text = preg_replace('/\s+/', ' ', $text);

        // Convert to proper case for names
        return mb_convert_case($text, MB_CASE_TITLE, 'UTF-8');
    }

    /**
     * Validate DNI format more strictly
     */
    private function isValidDni(string $dni): bool
    {
        // Remove any non-numeric characters
        $dni = preg_replace('/[^0-9]/', '', $dni);

        // Check length (typically 8-12 digits for most countries)
        if (strlen($dni) < 8 || strlen($dni) > 12) {
            return false;
        }

        // Additional validation can be added here based on country-specific rules
        return true;
    }

    /**
     * Obtener estadísticas de importación
     */
    public function getImportStats(): array
    {
        return $this->importStats;
    }

    public function setAdditionalData($data)
    {
        $this->additionalData = $data;
    }

    public function setCustomImportData($data)
    {
        $this->customImportData = $data;
    }
}
